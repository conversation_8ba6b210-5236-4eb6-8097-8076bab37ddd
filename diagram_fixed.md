# SIASN BKD Jateng - System Architecture & Project Timeline

## System Architecture Diagram

```mermaid
graph TD
    subgraph "Browser/Mobile Vue.js SPA"
        A[Vue.js Frontend]
    end

    subgraph "API Gateway"
        B(API Gateway)
    end

    subgraph "Core Microservices (Laravel 12)"
        C[User Service]
        D[PDM Service]
        E[Document Service]
        F[Workflow Service]
        G[Notification Service]
    end

    subgraph "Integration Layer"
        H[SIASN Integration]
    end

    subgraph "Data & Infrastructure"
        I[(Primary Database)]
        J[(Event Store)]
        K[(External SIASN API)]
    end

    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G
    D --> H
    F --> H
    H --> K
    C --> I
    D --> I
    E --> I
    F --> I
    G --> I
    I -.CDC Events.-> J
    J -.Sync.-> H
```

## Project Timeline

```mermaid
gantt
    title Jadwal Pelaksanaan Proyek Pengembangan SIASN BKD Jateng
    dateFormat YYYY-MM-DD
    axisFormat %d %b

    section Fase 1 Fondasi dan <PERSON>
    Sprint 0 Kick-off Setup Analisis    :crit, s0, 2025-10-01, 7d
    Laporan Awal                        :milestone, la, 2025-10-08, 0d

    section Fase 2 Pengembangan Inti
    Sprint 1 Auth dan User Service      :crit, s1, after s0, 7d
    Sprint 2 PDM Backend dan RBAC       :crit, s2, after s1, 7d

    section Fase 3 Fitur dan Integrasi
    Sprint 3 Modul Pemberkasan          :crit, s3, after s2, 7d
    Sprint 4 Dashboard dan Integrasi    :crit, s4, after s3, 7d

    section Fase 4 Pengujian dan UAT
    Sprint 5 Integration Testing        :crit, s5, after s4, 7d
    Sprint 6 UAT dan Bug Fixing         :crit, s6, after s5, 7d

    section Fase 5 Deployment dan Serah Terima
    Sprint 7 Deployment dan Training    :crit, s7, after s6, 7d
    Sprint 8 Finalisasi dan Handover    :crit, s8, after s7, 7d
    Laporan Akhir dan Serah Terima      :milestone, lf, 2025-11-30, 0d
```

```mermaid
graph TD
    subgraph BKD Jateng
        Client
    end

    subgraph Tim Konsultan
        TL1
        TL2
        
        TL1 -- Koordinasi & Laporan --> Client
        TL1 -- Mengelola Proses --> DevTeam
        TL2 -- Arah Teknis --> DevTeam

        subgraph "Development Team (DevTeam)"
            SA
            DBA
            PROG[Programmer (4)]
            NET[Ahli Jaringan Komputer]
        end

        subgraph "Support Team"
            ADM[Administrasi (2)]
        end

        TL2 -- Supervisi Teknis --> SA
        TL2 -- Supervisi Teknis --> DBA
        TL2 -- Supervisi Teknis --> PROG
        TL2 -- Supervisi Teknis --> NET
        TL1 -- Dukungan --> ADM
    end
```